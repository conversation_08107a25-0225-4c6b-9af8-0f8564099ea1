# MongoDB 服务配置信息表

| 服务名称 | 命名空间 | 行号及配置信息 |
|---------|---------|---------------|
| resource-manager | application.yml | 行号:37 - uri: mongodb://t3cc_rw:<EMAIL>:8635,mongodb-center-2-b-business.t3go.com.cn:8635/t3_cc?authSource=t3_cc&replicaSet=replica |
| maintain-biz-handle-center | application.yml | 行号:44 - uri: mongodb://pgm_user:<EMAIL>:8635,mongodb-resource-2-b-business.t3go.com.cn:8635/t3_resource?authSource=t3_resource&replicaSet=replica |
| maintain-biz-handle-api | application.yml | 行号:55 - uri: mongodb://pgm_user:<EMAIL>:8635,mongodb-resource-2-b-business.t3go.com.cn:8635/t3_resource?authSource=t3_resource&replicaSet=replica |
| kz-data-center-fp | application.yml | 行号:92 - uri: mongodb://pgm_rw:<EMAIL>:8635,mongodb-vehiclefeature-2-b-business.t3go.com.cn:8635/kz_data_center_fp?authSource=kz_data_center_fp&replicaSet=replica&slaveOk=true&write=1&readPreference=secondaryPreferred&connectTimeoutMS=300000 |
| resource-vehicle | application.yml | 行号:37 - uri: mongodb://t3cc_rw:<EMAIL>:8635,mongodb-center-2-b-business.t3go.com.cn:8635/t3_cc?authSource=t3_cc&replicaSet=replica |
| gis-poi | application.yml | 行号:11 - uri: mongodb://gis_poi_rw:{password}@mongodb-poi-1-b-gis.t3go.com.cn:8635,mongodb-poi-2-b-gis.t3go.com.cn:8635,mongodb-poi-3-b-gis.t3go.com.cn:8635,mongodb-poi-4-b-gis.t3go.com.cn:8635/t3_gis_poi?authSource=t3_gis_poi&replicaSet=replica&slaveOk=true&write=1&readPreference=secondaryPreferred&connectTimeoutMS=10000&socketTimeoutMS=3000 |
| marketing-center-fp | application.yml | 行号:48 - uri: mongodb://common_rw:<EMAIL>:8635,mongodb-common-2-b-paxr.t3go.com.cn:8635/t3_common_fp?replicaSet=replica |
| marketing | application.yml | 行号:23 - uri: mongodb://common_rw:<EMAIL>:8635,mongodb-common-2-b-paxr.t3go.com.cn:8635/t3_common_fp?replicaSet=replica |
| marketing-stat | application.yml | 行号:48 - uri: mongodb://mark_stat_rw:<EMAIL>:8635,mongodb-marketing-2-b-paxr.t3go.com.cn:8635/t3_marketing_stat?authSource=t3_marketing_stat&replicaSet=replica |
| marketing-activity-server-starter | application.yml | 行号:9 - uri: mongodb://mark_stat_rw:<EMAIL>:8635,mongodb-marketing-2-b-paxr.t3go.com.cn:8635/t3_marketing_stat?authSource=t3_marketing_stat&replicaSet=replica |
| marketing-interactive-server-starter | application.yml | 行号:15 - uri: mongodb://mark_stat_rw:<EMAIL>:8635,mongodb-marketing-2-b-paxr.t3go.com.cn:8635/t3_marketing_stat?authSource=t3_marketing_stat&replicaSet=replica |
| travel-manager | application.yml | 行号:36 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| travel-config | application.yml | 行号:24 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| operation-analysis | application.yml | 行号:7 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| operation-environment-runtime | application.yml | 行号:27 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| operation-computation-manager | application.yml | 行号:26 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| driver-core | application.yml | 行号:38 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |

## 统计信息

- 总服务数量：17个
- 配置文件类型：application.yml
- 主要数据库集群：
  - mongodb-center (business)
  - mongodb-resource (business) 
  - mongodb-vehiclefeature (business)
  - mongodb-poi (gis)
  - mongodb-common (paxr)
  - mongodb-marketing (paxr)
  - mongodb-drivercenter (driver)
