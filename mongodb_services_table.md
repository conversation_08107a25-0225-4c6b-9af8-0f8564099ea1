# MongoDB 服务配置信息表

| 服务名称 | 命名空间 | 行号及配置信息 |
|---------|---------|---------------|
| risk-control-account-api | application.yml | 行号:32 - uri: mongodb://dc_rw:<EMAIL>:3717,dds-bp1ba911616c01d42.mongodb.rds.aliyuncs.com:3717/t3_dc?replicaSet=mgset-******** |
| resource-manager | application.yml | 行号:37 - uri: mongodb://t3cc_rw:<EMAIL>:8635,mongodb-center-2-b-business.t3go.com.cn:8635/t3_cc?authSource=t3_cc&replicaSet=replica |
| maintain-biz-handle-api | application.yml | 行号:55 - uri: mongodb://pgm_user:<EMAIL>:8635,mongodb-resource-2-b-business.t3go.com.cn:8635/t3_resource?authSource=t3_resource&replicaSet=replica |
| maintain-biz-handle-center | application.yml | 行号:44 - uri: mongodb://pgm_user:<EMAIL>:8635,mongodb-resource-2-b-business.t3go.com.cn:8635/t3_resource?authSource=t3_resource&replicaSet=replica |
| kz-data-center-fp | application.yml | 行号:92 - uri: mongodb://pgm_rw:<EMAIL>:8635,mongodb-vehiclefeature-2-b-business.t3go.com.cn:8635/kz_data_center_fp?authSource=kz_data_center_fp&replicaSet=replica&slaveOk=true&write=1&readPreference=secondaryPreferred&connectTimeoutMS=300000 |
| resource-vehicle | application.yml | 行号:37 - uri: mongodb://t3cc_rw:<EMAIL>:8635,mongodb-center-2-b-business.t3go.com.cn:8635/t3_cc?authSource=t3_cc&replicaSet=replica |
| gis-vehicle-db | commonConfig.vehicle.application | 行号:18-91 - spring.data.dbx.securityReadonlyUri: mongodb://{username}:{password}@mongodb-vehiclefeaturex-x-b-strategy.t3go.com.cn:8635,mongodb-vehiclefeaturex-x-b-strategy.t3go.com.cn:8635,mongodb-vehiclefeaturex-x-b-strategy.t3go.com.cn:8635,mongodb-vehiclefeaturex-x-b-strategy.t3go.com.cn:8635/t3_gis?authSource=t3_gis&replicaSet=replica&slaveOk=true&write=1&readPreference=secondaryPreferred&connectTimeoutMS=10000&socketTimeoutMS=5000 |
| gis-poi | application.yml | 行号:11 - uri: mongodb://gis_poi_rw:{password}@mongodb-poi-1-b-gis.t3go.com.cn:8635,mongodb-poi-2-b-gis.t3go.com.cn:8635,mongodb-poi-3-b-gis.t3go.com.cn:8635,mongodb-poi-4-b-gis.t3go.com.cn:8635/t3_gis_poi?authSource=t3_gis_poi&replicaSet=replica&slaveOk=true&write=1&readPreference=secondaryPreferred&connectTimeoutMS=10000&socketTimeoutMS=3000 |
| operations-research-prediction | conf.yaml | 行号:35 - host: 'mongodb://pgm_user:B8DGJn7j#wFQjAGG@***********:8635,***********:8635/t3_operations_prediction?authSource=t3_operations_prediction&replicaSet=replica' #prd |
| marketing-center-fp | application.yml | 行号:48 - uri: mongodb://common_rw:<EMAIL>:8635,mongodb-common-2-b-paxr.t3go.com.cn:8635/t3_common_fp?replicaSet=replica |
| marketing | application.yml | 行号:23 - uri: mongodb://common_rw:<EMAIL>:8635,mongodb-common-2-b-paxr.t3go.com.cn:8635/t3_common_fp?replicaSet=replica |
| marketing-stat | application.yml | 行号:48 - uri: mongodb://mark_stat_rw:<EMAIL>:8635,mongodb-marketing-2-b-paxr.t3go.com.cn:8635/t3_marketing_stat?authSource=t3_marketing_stat&replicaSet=replica |
| marketing-interactive-server-starter | application.yml | 行号:15 - uri: mongodb://mark_stat_rw:<EMAIL>:8635,mongodb-marketing-2-b-paxr.t3go.com.cn:8635/t3_marketing_stat?authSource=t3_marketing_stat&replicaSet=replica |
| marketing-activity-server-starter | application.yml | 行号:9 - uri: mongodb://mark_stat_rw:<EMAIL>:8635,mongodb-marketing-2-b-paxr.t3go.com.cn:8635/t3_marketing_stat?authSource=t3_marketing_stat&replicaSet=replica |
| t3-dc-monitor-taxi-api | application.yml | 行号:11 - uri: mongodb://dc_rw:<EMAIL>:8635,mongodb-monitorscreen-2-c-t3.t3go.com.cn:8635/t3_dc?authSource=t3_dc&replicaSet=replica |
| travel-manager | application.yml | 行号:36 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| travel-config | application.yml | 行号:24 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| operation-analysis | application.yml | 行号:7 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| operation-environment-runtime | application.yml | 行号:27 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| operation-computation-manager | application.yml | 行号:26 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |
| driver-core | application.yml | 行号:38 - uri: mongodb://pgm_user:${spring.data.mongodb.password}@mongodb-drivercenter-1-b-driver.t3go.com.cn:8635,mongodb-drivercenter-2-b-driver.t3go.com.cn:8635/t3?authSource=t3&replicaSet=replica |

## 统计信息

- 总服务数量：21个
- 配置文件类型：
  - application.yml (19个服务)
  - commonConfig.vehicle.application (1个服务)
  - conf.yaml (1个服务)
- 主要数据库集群：
  - mongodb-center (business) - 2个服务
  - mongodb-resource (business) - 2个服务
  - mongodb-vehiclefeature (business) - 1个服务
  - mongodb-vehiclefeaturex (strategy) - 1个服务
  - mongodb-poi (gis) - 1个服务
  - mongodb-common (paxr) - 2个服务
  - mongodb-marketing (paxr) - 3个服务
  - mongodb-drivercenter (driver) - 6个服务
  - mongodb-monitorscreen (t3) - 1个服务
  - 阿里云RDS MongoDB - 1个服务
  - 内网IP MongoDB - 1个服务

## 新增服务

相比之前版本，新增了以下服务：
- risk-control-account-api (使用阿里云RDS MongoDB)
- gis-vehicle-db (使用不同的配置文件格式)
- operations-research-prediction (使用conf.yaml配置)
- t3-dc-monitor-taxi-api (使用mongodb-monitorscreen集群)
